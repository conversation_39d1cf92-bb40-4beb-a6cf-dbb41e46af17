import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { AlertCircle, Loader } from 'lucide-react';
import { signInWithEmail, signInWithMicrosoft } from '../firebase/auth';
import './LoginForm.css';

const Login: React.FC = () => {
  const navigate = useNavigate();
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      await signInWithEmail(email, password);

      // Let the App.tsx handle role-based redirect using the * route
      // This ensures users go to the correct dashboard based on their role
      // Admin users will be redirected to /admin/dashboard-new automatically
      navigate('/', { replace: true });
    } catch (err: unknown) {
      console.error('Login error:', err);
      setError(err instanceof Error ? err.message : 'Failed to sign in');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleMicrosoftLogin = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      await signInWithMicrosoft();
      navigate('/', { replace: true });
    } catch (err: unknown) {
      console.error('Microsoft login error:', err);
      setError(err instanceof Error ? err.message : 'Failed to sign in with Microsoft');
    } finally {
      setIsLoading(false);
    }
  };
  


  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-2 sm:p-4 overflow-x-hidden">
      <div className="w-full max-w-md">
        {/* Logo */}
        <div className="text-center mb-8">
          <img
            src="/hive-campus-logo.svg"
            alt="Hive Campus Logo"
            className="w-16 h-16 mx-auto mb-4"
          />
        </div>

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="form">
          <div className="title">
            Welcome Back <span>Sign in to your Hive Campus account</span>
          </div>

          {error && (
            <div className="error-message">
              <AlertCircle className="w-4 h-4 inline mr-2" />
              {error}
            </div>
          )}
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="input"
            placeholder="<EMAIL>"
            required
            disabled={isLoading}
          />

          <input
            type={showPassword ? 'text' : 'password'}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="input"
            placeholder="Enter your password"
            required
            disabled={isLoading}
          />

          <div className="login-with">
            <button
              type="button"
              onClick={handleMicrosoftLogin}
              className="button-log"
              disabled={isLoading}
            >
              <svg className="microsoft-icon" viewBox="0 0 23 23">
                <path fill="#f3f3f3" d="M0 0h23v23H0z"/>
                <path fill="#f35325" d="M1 1h10v10H1z"/>
                <path fill="#81bc06" d="M12 1h10v10H12z"/>
                <path fill="#05a6f0" d="M1 12h10v10H1z"/>
                <path fill="#ffba08" d="M12 12h10v10H12z"/>
              </svg>
            </button>
          </div>

          <button
            type="submit"
            className="button-confirm"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader className="w-4 h-4 animate-spin inline mr-2" />
                Signing in...
              </>
            ) : (
              'Sign In'
            )}
          </button>
        </form>

        {/* Sign Up Link */}
        <div className="mt-6 text-center">
          <p className="text-gray-600 dark:text-gray-400">
            Don't have an account?{' '}
            <Link to="/signup" className="text-primary-600 dark:text-primary-400 font-semibold hover:underline">
              Sign up
            </Link>
          </p>
        </div>
      </div>

    </div>
  );
};

export default Login;