import React from 'react';
import './HiveCampusLoader.css';

interface HiveCampusLoaderProps {
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

const HiveCampusLoader: React.FC<HiveCampusLoaderProps> = ({ 
  size = 'medium', 
  className = '' 
}) => {
  const sizeClasses = {
    small: 'w-24 h-24 text-sm',
    medium: 'w-36 h-36 text-base',
    large: 'w-44 h-44 text-lg'
  };

  const text = "HIVE CAMPUS";
  
  return (
    <div className={`hive-loader-wrapper ${sizeClasses[size]} ${className}`}>
      <div className="hive-loader"></div>
      <div className="hive-loader-text">
        {text.split('').map((letter, index) => (
          <span 
            key={index} 
            className="hive-loader-letter"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            {letter === ' ' ? '\u00A0' : letter}
          </span>
        ))}
      </div>
    </div>
  );
};

export default HiveCampusLoader;
