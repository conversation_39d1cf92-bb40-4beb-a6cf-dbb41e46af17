import React from 'react';
import HiveCampusLoader from './HiveCampusLoader';

const LoaderShowcase: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
          Hive Campus Loader Showcase
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Small Loader */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 text-center">
              Small
            </h2>
            <div className="flex justify-center">
              <HiveCampusLoader size="small" />
            </div>
          </div>
          
          {/* Medium Loader */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 text-center">
              Medium
            </h2>
            <div className="flex justify-center">
              <HiveCampusLoader size="medium" />
            </div>
          </div>
          
          {/* Large Loader */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 text-center">
              Large
            </h2>
            <div className="flex justify-center">
              <HiveCampusLoader size="large" />
            </div>
          </div>
        </div>
        
        {/* Dark Background Example */}
        <div className="mt-12 bg-gray-900 rounded-xl p-8">
          <h2 className="text-xl font-semibold text-white mb-6 text-center">
            On Dark Background
          </h2>
          <div className="flex justify-center">
            <HiveCampusLoader size="medium" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoaderShowcase;
