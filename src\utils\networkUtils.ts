/**
 * Network utilities for handling connection issues and browser blocking
 */

export interface NetworkDiagnostics {
  isOnline: boolean;
  connectionType: string;
  effectiveType: string;
  downlink: number;
  rtt: number;
  saveData: boolean;
}

/**
 * Get network diagnostics information
 */
export function getNetworkDiagnostics(): NetworkDiagnostics {
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
  
  return {
    isOnline: navigator.onLine,
    connectionType: connection?.type || 'unknown',
    effectiveType: connection?.effectiveType || 'unknown',
    downlink: connection?.downlink || 0,
    rtt: connection?.rtt || 0,
    saveData: connection?.saveData || false
  };
}

/**
 * Check if error is caused by ad blocker or browser extension
 */
export function isBlockedByClient(error: Error | string): boolean {
  const message = typeof error === 'string' ? error : error.message;
  const lowerMessage = message.toLowerCase();
  
  const blockedKeywords = [
    'err_blocked_by_client',
    'blocked by client',
    'net::err_blocked_by_client',
    'failed to fetch',
    'network error',
    'cors error',
    'blocked by extension'
  ];
  
  return blockedKeywords.some(keyword => lowerMessage.includes(keyword));
}

/**
 * Check if error is a network timeout
 */
export function isNetworkTimeout(error: Error | string): boolean {
  const message = typeof error === 'string' ? error : error.message;
  const lowerMessage = message.toLowerCase();
  
  const timeoutKeywords = [
    'timeout',
    'deadline-exceeded',
    'request timeout',
    'connection timeout',
    'network timeout'
  ];
  
  return timeoutKeywords.some(keyword => lowerMessage.includes(keyword));
}

/**
 * Get user-friendly error message for network issues
 */
export function getNetworkErrorMessage(error: Error | string): string {
  if (isBlockedByClient(error)) {
    return 'Connection blocked by browser or extension. Try disabling ad blockers or using incognito mode.';
  }
  
  if (isNetworkTimeout(error)) {
    return 'Request timed out. Please check your internet connection and try again.';
  }
  
  if (!navigator.onLine) {
    return 'You appear to be offline. Please check your internet connection.';
  }
  
  const diagnostics = getNetworkDiagnostics();
  if (diagnostics.effectiveType === 'slow-2g' || diagnostics.effectiveType === '2g') {
    return 'Slow connection detected. Some features may take longer to load.';
  }
  
  return 'Network error occurred. Please try again.';
}

/**
 * Test Firebase connectivity
 */
export async function testFirebaseConnectivity(): Promise<{
  success: boolean;
  error?: string;
  latency?: number;
}> {
  const startTime = Date.now();
  
  try {
    // Test basic connectivity to Firebase
    const response = await fetch('https://firestore.googleapis.com/', {
      method: 'HEAD',
      mode: 'no-cors'
    });
    
    const latency = Date.now() - startTime;
    
    return {
      success: true,
      latency
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      latency: Date.now() - startTime
    };
  }
}

/**
 * Retry function with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  maxDelay: number = 10000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      // Don't retry on certain errors
      if (isBlockedByClient(lastError)) {
        throw lastError;
      }
      
      // If this was the last attempt, throw the error
      if (attempt === maxRetries - 1) {
        throw lastError;
      }
      
      // Calculate delay with exponential backoff
      const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
      
      console.log(`Retry attempt ${attempt + 1}/${maxRetries} after ${delay}ms delay`);
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError!;
}

/**
 * Check if current environment might have network restrictions
 */
export function detectNetworkRestrictions(): {
  hasAdBlocker: boolean;
  hasStrictCSP: boolean;
  hasFirewall: boolean;
  recommendations: string[];
} {
  const recommendations: string[] = [];
  let hasAdBlocker = false;
  let hasStrictCSP = false;
  let hasFirewall = false;
  
  // Check for ad blocker (basic detection)
  try {
    const testElement = document.createElement('div');
    testElement.innerHTML = '&nbsp;';
    testElement.className = 'adsbox';
    document.body.appendChild(testElement);
    
    if (testElement.offsetHeight === 0) {
      hasAdBlocker = true;
      recommendations.push('Disable ad blocker for this site');
    }
    
    document.body.removeChild(testElement);
  } catch (error) {
    // Ignore errors in ad blocker detection
  }
  
  // Check for strict CSP by testing inline script execution
  try {
    const script = document.createElement('script');
    script.textContent = '1+1';
    document.head.appendChild(script);
    document.head.removeChild(script);
  } catch (error) {
    hasStrictCSP = true;
    recommendations.push('Browser security settings may be blocking connections');
  }
  
  // Check for potential firewall issues
  const userAgent = navigator.userAgent.toLowerCase();
  if (userAgent.includes('corporate') || userAgent.includes('enterprise')) {
    hasFirewall = true;
    recommendations.push('Corporate firewall may be blocking Firebase connections');
  }
  
  // General recommendations
  if (hasAdBlocker || hasStrictCSP || hasFirewall) {
    recommendations.push('Try using incognito/private browsing mode');
    recommendations.push('Check browser console for blocked requests');
    recommendations.push('Contact your network administrator if on corporate network');
  }
  
  return {
    hasAdBlocker,
    hasStrictCSP,
    hasFirewall,
    recommendations
  };
}

/**
 * Log network diagnostics for debugging
 */
export function logNetworkDiagnostics(): void {
  const diagnostics = getNetworkDiagnostics();
  const restrictions = detectNetworkRestrictions();
  
  console.group('🌐 Network Diagnostics');
  console.log('Online:', diagnostics.isOnline);
  console.log('Connection Type:', diagnostics.connectionType);
  console.log('Effective Type:', diagnostics.effectiveType);
  console.log('Downlink:', diagnostics.downlink, 'Mbps');
  console.log('RTT:', diagnostics.rtt, 'ms');
  console.log('Save Data:', diagnostics.saveData);
  console.log('Ad Blocker Detected:', restrictions.hasAdBlocker);
  console.log('Strict CSP:', restrictions.hasStrictCSP);
  console.log('Potential Firewall:', restrictions.hasFirewall);
  
  if (restrictions.recommendations.length > 0) {
    console.log('Recommendations:', restrictions.recommendations);
  }
  
  console.groupEnd();
}
