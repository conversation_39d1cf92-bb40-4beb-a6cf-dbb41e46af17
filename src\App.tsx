import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import Layout from './components/Layout';
import ConditionalLayout from './components/ConditionalLayout';
import ProtectedRoute from './components/ProtectedRoute';
import InstallPrompt from './components/InstallPrompt';
import NetworkStatus from './components/NetworkStatus';
import PWAUpdateNotification from './components/PWAUpdateNotification';
// import SentryRouteTracker from './components/SentryRouteTracker';
import ReeFlexTracker from './components/ReeFlexTracker';
import HiveCampusLoader from './components/HiveCampusLoader';

import ErrorBoundary from './components/ErrorBoundary';
import { AuthProvider } from './contexts/AuthContext';
import { useAuth } from './hooks/useAuth';

// Pages
import LandingPage from './pages/LandingPage';
import LoginTypeSelection from './pages/LoginTypeSelection';
import Login from './pages/Login';
import Signup from './pages/Signup';
import MerchantLogin from './pages/MerchantLogin';
import MerchantSignup from './pages/MerchantSignup';
import MerchantDashboard from './pages/MerchantDashboard';
import Home from './pages/Home';
import Search from './pages/Search';
import AddListing from './pages/AddListing';
import ViewListing from './pages/ViewListing';
import EditListing from './pages/EditListing';
import Profile from './pages/Profile';
import PublicProfile from './pages/PublicProfile';
import Settings from './pages/Settings';
import PrivacyPolicy from './pages/PrivacyPolicy';
import TermsAndConditions from './pages/TermsAndConditions';
import WalletPage from './pages/WalletPage';
import Checkout from './pages/CheckoutNew';
import CheckoutSuccessPage from './pages/CheckoutSuccessPage';
import OrderSuccess from './pages/OrderSuccess';
import PaymentSettingsPage from './pages/PaymentSettingsPage';
import Feedback from './pages/Feedback';
import ReportIssue from './pages/ReportIssue';
import OrderTracking from './pages/OrderTracking';
import OrderHistory from './pages/OrderHistory';
import AdminPanel from './components/admin/AdminPanel';
import Messages from './pages/Messages';
import AboutUs from './pages/AboutUs';
import ContactUs from './pages/ContactUs';
import SellerProfile from './pages/SellerProfile';

import MerchantProfile from './pages/MerchantProfile';
import MerchantMessages from './pages/MerchantMessages';
import MerchantSettings from './pages/MerchantSettings';
import MerchantAboutUs from './pages/MerchantAboutUs';
import MerchantContactUs from './pages/MerchantContactUs';
import MerchantFeedback from './pages/MerchantFeedback';
import MerchantReportIssue from './pages/MerchantReportIssue';
import MerchantDirectory from './pages/MerchantDirectory';
import DebugListings from './pages/DebugListings';
import EmailVerification from './pages/EmailVerification';

// Main App Component with Routes
const AppRoutes = () => {
  const { currentUser, userRole, isLoading } = useAuth();
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Check for dark mode preference
  useEffect(() => {
    const darkModePreference = localStorage.getItem('darkMode') === 'true';
    setIsDarkMode(darkModePreference);
    if (darkModePreference) {
      document.documentElement.classList.add('dark');
    }
  }, []);

  const toggleDarkMode = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);
    localStorage.setItem('darkMode', String(newDarkMode));
    document.documentElement.classList.toggle('dark');
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className={`min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 ${isDarkMode ? 'dark' : ''}`}>
        <HiveCampusLoader size="large" />
      </div>
    );
  }

  return (
    <div className={isDarkMode ? 'dark' : ''}>
      <InstallPrompt />
      <NetworkStatus />
      <PWAUpdateNotification />
      <ReeFlexTracker />

      <Toaster
        position="top-center"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            iconTheme: {
              primary: '#10b981',
              secondary: '#fff',
            },
          },
          error: {
            duration: 5000,
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
          },
        }}
      />
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={
          !currentUser ? <LandingPage /> : (
            userRole === 'admin' ? <Navigate to="/admin/dashboard" /> :
            userRole === 'merchant' ? <Navigate to="/merchant/dashboard" /> :
            userRole === 'student' ? <Navigate to="/home" /> :
            <Navigate to="/home" /> // Default fallback
          )
        } />
        <Route path="/login-type" element={<LoginTypeSelection />} />
        <Route path="/signup" element={<Signup />} />
        <Route path="/login" element={<Login />} />
        <Route path="/verify-email" element={<EmailVerification />} />
        <Route path="/merchant-signup" element={<MerchantSignup />} />
        <Route path="/merchant-login" element={<MerchantLogin />} />
        <Route path="/privacy" element={<PrivacyPolicy />} />
        <Route path="/terms" element={<TermsAndConditions />} />
        <Route path="/about" element={
          <ConditionalLayout>
            <AboutUs />
          </ConditionalLayout>
        } />
        <Route path="/contact" element={
          <ConditionalLayout>
            <ContactUs />
          </ConditionalLayout>
        } />

        <Route path="/listing/:id" element={
          <ConditionalLayout>
            <ViewListing />
          </ConditionalLayout>
        } />
        <Route path="/seller/:sellerId" element={
          <ConditionalLayout>
            <SellerProfile />
          </ConditionalLayout>
        } />
        <Route path="/merchant/:merchantId" element={
          <ConditionalLayout>
            <MerchantProfile />
          </ConditionalLayout>
        } />
        <Route path="/merchants" element={
          <ConditionalLayout>
            <MerchantDirectory />
          </ConditionalLayout>
        } />

        {/* Student Routes */}
        <Route path="/home" element={
          <ProtectedRoute allowedRoles={['student']}>
            <Layout>
              <Home />
            </Layout>
          </ProtectedRoute>
        } />

        <Route path="/search" element={
          <ProtectedRoute allowedRoles={['student']}>
            <Layout>
              <Search />
            </Layout>
          </ProtectedRoute>
        } />

        <Route path="/add-listing" element={
          <ProtectedRoute allowedRoles={['student']}>
            <Layout>
              <AddListing />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/edit-listing/:id" element={
          <ProtectedRoute allowedRoles={['student']}>
            <Layout>
              <EditListing />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/profile" element={
          <ProtectedRoute>
            <Layout>
              <Profile />
            </Layout>
          </ProtectedRoute>
        } />

        <Route path="/user/:userId" element={
          <ProtectedRoute>
            <Layout>
              <PublicProfile />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/settings" element={
          <ProtectedRoute>
            <Layout>
              <Settings toggleDarkMode={toggleDarkMode} isDarkMode={isDarkMode} />
            </Layout>
          </ProtectedRoute>
        } />

        <Route path="/debug-listings" element={
          <ProtectedRoute>
            <Layout>
              <DebugListings />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/wallet" element={
          <ProtectedRoute allowedRoles={['student']}>
            <Layout>
              <WalletPage />
            </Layout>
          </ProtectedRoute>
        } />

        <Route path="/checkout/:listingId" element={
          <ProtectedRoute allowedRoles={['student']}>
            <Layout>
              <Checkout />
            </Layout>
          </ProtectedRoute>
        } />

        {/* Fallback route for /checkout without listingId */}
        <Route path="/checkout" element={<Navigate to="/home" replace />} />
        
        <Route path="/order/success" element={
          <ProtectedRoute allowedRoles={['student']}>
            <CheckoutSuccessPage />
          </ProtectedRoute>
        } />

        <Route path="/order-success" element={
          <ProtectedRoute allowedRoles={['student']}>
            <OrderSuccess />
          </ProtectedRoute>
        } />
        
        <Route path="/settings/payment" element={
          <ProtectedRoute>
            <Layout>
              <PaymentSettingsPage />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/feedback" element={
          <ProtectedRoute>
            <Layout>
              <Feedback />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/report" element={
          <ProtectedRoute>
            <Layout>
              <ReportIssue />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/order/:orderId" element={
          <ProtectedRoute allowedRoles={['student']}>
            <Layout>
              <OrderTracking />
            </Layout>
          </ProtectedRoute>
        } />

        <Route path="/order-history" element={
          <ProtectedRoute>
            <Layout>
              <OrderHistory />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/messages" element={
          <ProtectedRoute>
            <Layout>
              <Messages />
            </Layout>
          </ProtectedRoute>
        } />

        {/* Merchant Routes - now using integrated Layout */}
        <Route path="/merchant/dashboard" element={
          <ProtectedRoute allowedRoles={['merchant']}>
            <Layout>
              <MerchantDashboard />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/merchant/profile" element={
          <ProtectedRoute allowedRoles={['merchant']}>
            <Layout>
              <MerchantProfile />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/merchant/messages" element={
          <ProtectedRoute allowedRoles={['merchant']}>
            <Layout>
              <MerchantMessages />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/merchant/settings" element={
          <ProtectedRoute allowedRoles={['merchant']}>
            <Layout>
              <MerchantSettings toggleDarkMode={toggleDarkMode} isDarkMode={isDarkMode} />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/merchant/about" element={
          <ProtectedRoute allowedRoles={['merchant']}>
            <Layout>
              <MerchantAboutUs />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/merchant/contact" element={
          <ProtectedRoute allowedRoles={['merchant']}>
            <Layout>
              <MerchantContactUs />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/merchant/feedback" element={
          <ProtectedRoute allowedRoles={['merchant']}>
            <Layout>
              <MerchantFeedback />
            </Layout>
          </ProtectedRoute>
        } />
        
        <Route path="/merchant/report" element={
          <ProtectedRoute allowedRoles={['merchant']}>
            <Layout>
              <MerchantReportIssue />
            </Layout>
          </ProtectedRoute>
        } />

        {/* Admin Routes - New Admin Panel */}
        <Route path="/admin/*" element={
          <ProtectedRoute allowedRoles={['admin']}>
            <AdminPanel />
          </ProtectedRoute>
        } />

        {/* Redirect based on role */}
        <Route path="*" element={
          currentUser ? (
            userRole === 'admin' ? <Navigate to="/admin/dashboard" /> :
            userRole === 'merchant' ? <Navigate to="/merchant/dashboard" /> :
            <Navigate to="/home" />
          ) : (
            <Navigate to="/" />
          )
        } />
      </Routes>
    </div>
  );
};


// Wrap the app with AuthProvider and Router
function App() {
  return (
    <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
      <AuthProvider>
        <ErrorBoundary>
          <AppRoutes />
        </ErrorBoundary>
      </AuthProvider>
    </Router>
  );
}

export default App;