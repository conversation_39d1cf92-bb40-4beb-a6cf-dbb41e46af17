import React, { useState, useRef, useEffect, useMemo } from 'react';
import {
  MessageCircle,
  Send,
  Image,
  Camera,
  MoreVertical,
  Search,
  ArrowLeft,
  Trash2,
  VolumeX,
  Flag,
  Check,
  CheckCheck,
  Loader2,
  X
} from 'lucide-react';
import { useLocation } from 'react-router-dom';
import toast from 'react-hot-toast';
import { useAuth } from '../hooks/useAuth';
import { useMessaging } from '../hooks/useMessaging';
import { createOrGetChat } from '../firebase/messages';
import { isRiskyMessage, getRiskType, getRiskErrorMessage, logViolation } from '../utils/detectRisk';

const Messages: React.FC = () => {
  const location = useLocation();
  const { currentUser } = useAuth();
  const {
    chats,
    messages,
    users,
    isLoading,
    sendChatMessage,
    sendChatImageMessage,
    loadChats,
    subscribeToChats,
    subscribeToMessages,
    setCurrentChat,
    markAsRead
  } = useMessaging();

  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [_isCreatingChat, setIsCreatingChat] = useState(false);
  const [showChatMenu, setShowChatMenu] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isMobile, setIsMobile] = useState(false);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [showImageOptions, setShowImageOptions] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageInputRef = useRef<HTMLInputElement>(null);

  // Optimized mobile detection with debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const checkMobile = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsMobile(window.innerWidth < 768);
      }, 100);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => {
      window.removeEventListener('resize', checkMobile);
      clearTimeout(timeoutId);
    };
  }, []);

  // Memoized filtered chats for better performance
  const filteredChats = useMemo(() => {
    if (!searchQuery.trim()) return chats;

    const query = searchQuery.toLowerCase();
    return chats.filter(chat => {
      const otherUser = users[chat.participants.find(p => p !== currentUser?.uid) || ''];
      const userName = otherUser?.name?.toLowerCase() || '';
      const lastMessage = chat.lastMessage?.text?.toLowerCase() || '';

      return userName.includes(query) || lastMessage.includes(query);
    });
  }, [chats, searchQuery, users, currentUser?.uid]);

  // Memoized current chat data
  const currentChatData = useMemo(() => {
    if (!selectedChatId) return null;
    return chats.find(chat => chat.id === selectedChatId) || null;
  }, [chats, selectedChatId]);

  // Memoized other user data
  const _otherUser = useMemo(() => {
    if (!currentChatData || !currentUser) return null;
    const otherUserId = currentChatData.participants.find(p => p !== currentUser.uid);
    return otherUserId ? users[otherUserId] : null;
  }, [currentChatData, currentUser, users]);

  // Handle chat initiation from listing or profile
  useEffect(() => {
    const initiateChatFromNavigation = async () => {
      const state = location.state as {
        sellerId?: string;
        listingId?: string;
        chatId?: string;
        otherUserId?: string;
      } | null;

      if (!currentUser) return;

      // Handle chat from listing or order success
      if (state?.sellerId) {
        setIsCreatingChat(true);
        try {
          console.log('🔗 Creating chat with seller:', state.sellerId);

          const result = await createOrGetChat(state.sellerId, {
            listingId: state.listingId || 'order-chat',
            listingTitle: state.orderId ? `Order #${state.orderId.slice(-8)}` : 'Product Inquiry',
            // Don't include listingImageURL if it's undefined
          });

          console.log('💬 Chat creation result:', result);

          if (result && (result as { success: boolean; data: { chatId: string } }).success) {
            const chatId = (result as { success: boolean; data: { chatId: string } }).data.chatId;
            console.log('✅ Setting selected chat ID:', chatId);
            setSelectedChatId(chatId);
          } else if (result && typeof result === 'string') {
            // If result is just a chatId string
            console.log('✅ Setting selected chat ID (string):', result);
            setSelectedChatId(result);
          }
        } catch (error) {
          console.error('❌ Error creating chat:', error);
          // Show user-friendly error message
          alert('Failed to start chat with seller. Please try again.');
        } finally {
          setIsCreatingChat(false);
        }
      }

      // Handle chat from profile (direct chat selection)
      else if (state?.chatId) {
        setSelectedChatId(state.chatId);
      }

      // Handle chat creation from profile
      else if (state?.otherUserId) {
        setIsCreatingChat(true);
        try {
          const result = await createOrGetChat(state.otherUserId);
          if (result && (result as { success: boolean; data: { chatId: string } }).success) {
            setSelectedChatId((result as { success: boolean; data: { chatId: string } }).data.chatId);
          }
        } catch (error) {
          console.error('Error creating chat:', error);
        } finally {
          setIsCreatingChat(false);
        }
      }
    };

    initiateChatFromNavigation();
  }, [location.state, currentUser]);

  // Load chats when component mounts
  useEffect(() => {
    if (currentUser) {
      loadChats(currentUser.uid);
      const unsubscribe = subscribeToChats(currentUser.uid);
      return () => unsubscribe && unsubscribe();
    }
  }, [currentUser, loadChats, subscribeToChats]);

  // Subscribe to messages when a chat is selected and mark as read
  useEffect(() => {
    if (selectedChatId) {
      const unsubscribe = subscribeToMessages(selectedChatId);

      // Mark messages as read when viewing the chat
      const markMessagesAsRead = async () => {
        try {
          await markAsRead(selectedChatId);
        } catch (error) {
          console.error('Failed to mark messages as read:', error);
        }
      };

      markMessagesAsRead();

      return () => unsubscribe && unsubscribe();
    }
  }, [selectedChatId, subscribeToMessages, markAsRead]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messages.length > 0) {
      // Use a small delay to ensure the DOM has updated
      const timer = setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [messages]);

  // Close image options when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setShowImageOptions(false);
    };

    if (showImageOptions) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showImageOptions]);

  // Get selected chat data
  const selectedChatData = chats.find(chat => chat.id === selectedChatId);

  const handleSendMessage = async () => {
    if (newMessage.trim() && selectedChatId && selectedChatData && !sendingMessage) {
      const messageContent = newMessage.trim();

      // Check for risky content before sending
      if (isRiskyMessage(messageContent)) {
        const riskType = getRiskType(messageContent);
        const errorMessage = getRiskErrorMessage(riskType);

        // Show error toast to user
        toast.error(errorMessage);

        // Log the violation
        await logViolation('chat', messageContent, riskType, {
          chatId: selectedChatId,
          recipientId: selectedChatData.participants.find(id => id !== currentUser?.uid)
        });

        return; // Don't send the message
      }

      setSendingMessage(true);
      try {
        // Find the other user in the chat
        const otherUserId = selectedChatData.participants.find(id => id !== currentUser?.uid);
        if (!otherUserId) {
          throw new Error('Could not find recipient');
        }

        await sendChatMessage(selectedChatId, messageContent, otherUserId);
        setNewMessage('');

        // Auto-focus input and scroll to bottom after sending
        setTimeout(() => {
          messageInputRef.current?.focus();
          messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      } catch (error) {
        console.error('Failed to send message:', error);
        toast.error('Failed to send message. Please try again.');
      } finally {
        setSendingMessage(false);
      }
    }
  };

  // Handle image selection from gallery
  const handleImageFromGallery = () => {
    console.log('Gallery button clicked');
    console.log('fileInputRef.current:', fileInputRef.current);
    if (fileInputRef.current) {
      console.log('Clicking file input...');
      fileInputRef.current.click();
    } else {
      console.error('fileInputRef.current is null!');
    }
    setShowImageOptions(false);
  };

  // Handle image capture from camera
  const handleImageFromCamera = () => {
    console.log('Camera button clicked');
    cameraInputRef.current?.click();
    setShowImageOptions(false);
  };

  // Handle file selection (both gallery and camera)
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('handleFileChange called');
    const files = e.target.files;
    console.log('Files selected:', files);

    if (files && files.length > 0) {
      const file = files[0];
      console.log('Selected file:', {
        name: file.name,
        size: file.size,
        type: file.type
      });

      // Validate file type
      if (!file.type.startsWith('image/')) {
        console.error('Invalid file type:', file.type);
        alert('Please select an image file');
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        console.error('File too large:', file.size);
        alert('Image size must be less than 5MB');
        return;
      }

      console.log('File validation passed, setting selected image');
      setSelectedImage(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        console.log('File reader loaded, setting preview');
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }

    // Reset input
    e.target.value = '';
  };

  // Send image message
  const handleSendImage = async () => {
    console.log('handleSendImage called');
    console.log('State check:', {
      hasSelectedImage: !!selectedImage,
      selectedChatId,
      hasSelectedChatData: !!selectedChatData,
      uploadingImage
    });

    if (selectedImage && selectedChatId && selectedChatData && !uploadingImage) {
      console.log('Starting image upload...');
      setUploadingImage(true);
      try {
        // Find the other user in the chat
        const otherUserId = selectedChatData.participants.find(id => id !== currentUser?.uid);
        console.log('Other user ID:', otherUserId);

        if (!otherUserId) {
          throw new Error('Could not find recipient');
        }

        console.log('Calling sendChatImageMessage...');
        await sendChatImageMessage(selectedChatId, selectedImage, otherUserId);
        console.log('Image message sent successfully');

        // Clear image selection
        setSelectedImage(null);
        setImagePreview(null);

        // Auto-scroll to bottom after sending
        setTimeout(() => {
          messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
        }, 100);
      } catch (error) {
        console.error('Failed to send image:', error);
        alert(`Failed to send image: ${error instanceof Error ? error.message : 'Unknown error'}`);
      } finally {
        setUploadingImage(false);
      }
    } else {
      console.log('Cannot send image - missing requirements');
    }
  };

  // Cancel image selection
  const handleCancelImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
  };

  const handleChatAction = (action: string) => {
    console.log(`Chat action: ${action}`);
    setShowChatMenu(false);
    
    switch (action) {
      case 'delete':
        // Handle delete chat
        break;
      case 'mute':
        // Handle mute chat
        break;
      case 'report':
        // Handle report chat
        break;
    }
  };



  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 overflow-x-hidden fixed inset-0 md:left-64">
      <div className="max-w-7xl mx-auto px-1 sm:px-2 md:px-4 lg:px-6 py-2 sm:py-4 md:py-6 h-full w-full">
        <div className="bg-white dark:bg-gray-800 rounded-lg sm:rounded-2xl shadow-lg overflow-hidden h-[calc(100vh-1rem)] sm:h-[calc(100vh-2rem)] md:h-[calc(100vh-3rem)] w-full">
          <div className="flex h-full">
            {/* Chat List Sidebar */}
            <div className={`w-full md:w-1/3 border-r border-gray-200 dark:border-gray-700 flex flex-col ${selectedChatId && isMobile ? 'hidden' : 'flex'}`}>
              {/* Header */}
              <div className="p-3 sm:p-4 md:p-6 border-b border-gray-200 dark:border-gray-700">
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-3 sm:mb-4">Messages</h1>
                <div className="relative">
                  <label htmlFor="searchConversations" className="sr-only">Search conversations</label>
                  <Search className="absolute left-3 top-2.5 sm:top-3 w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                  <input
                    type="text"
                    id="searchConversations"
                    name="searchConversations"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search conversations..."
                    className="w-full pl-9 sm:pl-10 pr-3 sm:pr-4 py-2 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm sm:text-base"
                  />
                </div>
              </div>

              {/* Chat List */}
              <div className="flex-1 overflow-y-auto">
                {filteredChats.length === 0 ? (
                  <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                    {searchQuery ? 'No conversations found' : 'No conversations yet'}
                  </div>
                ) : (
                  filteredChats.map((chat) => {
                    const otherUserId = chat.participants.find(id => id !== currentUser?.uid);
                    const otherUser = otherUserId ? users[otherUserId] : null;
                    const userName = otherUser?.name || 'Unknown User';
                    const userAvatar = otherUser?.avatar || '/placeholder-avatar.jpg';

                    return (
                      <div
                        key={chat.id}
                        onClick={() => {
                          setSelectedChatId(chat.id);
                          setCurrentChat(chat);
                        }}
                        className={`p-4 border-b border-gray-100 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                          selectedChatId === chat.id ? 'bg-primary-50 dark:bg-primary-900/20 border-r-2 border-r-primary-500' : ''
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <div className="relative">
                            <img
                              src={userAvatar}
                              alt={userName}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                            {userName}
                          </h3>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {chat.lastMessage?.createdAt ? (() => {
                              const timestamp = chat.lastMessage.createdAt as any;
                              const seconds = timestamp.seconds || timestamp._seconds;
                              return seconds ? new Date(seconds * 1000).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : '';
                            })() : ''}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                            {chat.lastMessage?.type === 'image' ? (
                              <span className="flex items-center space-x-1">
                                <Image className="w-3 h-3" />
                                <span>Photo</span>
                              </span>
                            ) : (
                              chat.lastMessage?.text || 'No messages yet'
                            )}
                          </p>
                          {chat.unreadCount && chat.unreadCount > 0 && (
                            <span className="bg-primary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                              {chat.unreadCount}
                            </span>
                          )}
                        </div>
                        {/* Listing Preview */}
                        {chat.listingContext && (
                          <div className="flex items-center space-x-2 mt-2 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <img
                              src={chat.listingContext.listingImageURL || '/placeholder-image.jpg'}
                              alt={chat.listingContext.listingTitle}
                            className="w-8 h-8 rounded object-cover"
                          />
                            <div className="flex-1 min-w-0">
                              <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                                {chat.listingContext?.listingTitle || 'General conversation'}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                    );
                  })
                )}
                
              </div>
            </div>

            {/* Chat Window */}
            {selectedChatId && selectedChatData ? (
              <div className={`flex-1 flex flex-col ${selectedChatId ? 'flex' : 'hidden md:flex'}`}>
                {/* Chat Header */}
                <div className="p-3 sm:p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 sm:space-x-3">
                      <button
                        onClick={() => setSelectedChatId(null)}
                        className="md:hidden p-1.5 sm:p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5" />
                      </button>
                      {(() => {
                        const otherUserId = selectedChatData.participants.find(id => id !== currentUser?.uid);
                        const otherUser = otherUserId ? users[otherUserId] : null;
                        const userName = otherUser?.name || 'Unknown User';
                        const userAvatar = otherUser?.avatar || '/placeholder-avatar.jpg';

                        return (
                          <>
                            <div className="relative">
                              <img
                                src={userAvatar}
                                alt={userName}
                                className="w-10 h-10 rounded-full object-cover"
                              />
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900 dark:text-white">
                                {userName}
                              </h3>
                              <p className="text-sm text-gray-500 dark:text-gray-400">
                                Active recently
                              </p>
                            </div>
                          </>
                        );
                      })()}
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="relative">
                        <button
                          onClick={() => setShowChatMenu(!showChatMenu)}
                          className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                          <MoreVertical className="w-5 h-5" />
                        </button>
                        {showChatMenu && (
                          <div className="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl shadow-lg z-50">
                            <button
                              onClick={() => handleChatAction('mute')}
                              className="w-full text-left px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center space-x-3"
                            >
                              <VolumeX className="w-4 h-4" />
                              <span>Mute Chat</span>
                            </button>
                            <button
                              onClick={() => handleChatAction('report')}
                              className="w-full text-left px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center space-x-3"
                            >
                              <Flag className="w-4 h-4" />
                              <span>Report User</span>
                            </button>
                            <button
                              onClick={() => handleChatAction('delete')}
                              className="w-full text-left px-4 py-3 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center space-x-3 rounded-b-xl"
                            >
                              <Trash2 className="w-4 h-4" />
                              <span>Delete Chat</span>
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-3 sm:p-4 space-y-2 sm:space-y-3 bg-gray-50 dark:bg-gray-900">
                  {isLoading && messages.length === 0 ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                        <Loader2 className="w-5 h-5 animate-spin" />
                        <span>Loading messages...</span>
                      </div>
                    </div>
                  ) : messages.length === 0 ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center text-gray-500 dark:text-gray-400">
                        <MessageCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>No messages yet</p>
                        <p className="text-sm">Start the conversation!</p>
                      </div>
                    </div>
                  ) : (
                    messages.map((message) => {
                      const isOwnMessage = message.senderId === currentUser?.uid;
                      const messageTime = (() => {
                        const timestamp = message.createdAt as any;
                        const seconds = timestamp?.seconds || timestamp?._seconds;
                        return seconds ? new Date(seconds * 1000).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : '';
                      })();

                      return (
                        <div
                          key={message.id}
                          className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
                        >
                          <div
                            className={`max-w-[280px] sm:max-w-xs md:max-w-sm lg:max-w-md rounded-2xl shadow-sm ${
                              isOwnMessage
                                ? 'bg-primary-500 text-white rounded-br-md'
                                : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-bl-md border border-gray-200 dark:border-gray-700'
                            } ${message.type === 'image' ? 'p-1' : 'px-3 py-2'}`}
                          >
                            {message.type === 'image' && message.imageData ? (
                              <div className="relative">
                                <img
                                  src={message.imageData.url}
                                  alt={message.imageData.fileName}
                                  className="w-full h-auto rounded-xl max-w-full cursor-pointer hover:opacity-90 transition-opacity"
                                  onClick={() => window.open(message.imageData!.url, '_blank')}
                                  loading="lazy"
                                />
                                <div className={`absolute bottom-1 right-1 px-2 py-1 rounded-lg bg-black bg-opacity-50 ${
                                  isOwnMessage ? 'text-white' : 'text-white'
                                }`}>
                                  <div className="flex items-center space-x-1">
                                    <span className="text-xs">{messageTime}</span>
                                    {isOwnMessage && (
                                      <div className="flex items-center">
                                        {message.read ? (
                                          <CheckCheck className="w-3 h-3 text-white" />
                                        ) : (
                                          <Check className="w-3 h-3 text-white" />
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <>
                                <p className="text-sm leading-relaxed break-words">{message.text}</p>
                                <div className={`flex items-center justify-end mt-1 space-x-1 ${
                                  isOwnMessage ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
                                }`}>
                                  <span className="text-xs">{messageTime}</span>
                                  {isOwnMessage && (
                                    <div className="flex items-center">
                                      {message.read ? (
                                        <CheckCheck className="w-3 h-3 text-blue-200" />
                                      ) : (
                                        <Check className="w-3 h-3 text-blue-200" />
                                      )}
                                    </div>
                                  )}
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      );
                    })
                  )}
                  {/* Auto-scroll reference */}
                  <div ref={messagesEndRef} />
                </div>

                {/* Image Preview */}
                {imagePreview && (
                  <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-750">
                    <div className="relative inline-block">
                      <img
                        src={imagePreview}
                        alt="Preview"
                        className="max-w-xs max-h-32 rounded-lg object-cover"
                      />
                      <button
                        onClick={handleCancelImage}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                    <div className="mt-2 flex space-x-2">
                      <button
                        onClick={handleSendImage}
                        disabled={uploadingImage}
                        className="px-3 py-1 bg-primary-500 text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                      >
                        {uploadingImage ? (
                          <div className="flex items-center space-x-1">
                            <Loader2 className="w-3 h-3 animate-spin" />
                            <span>Sending...</span>
                          </div>
                        ) : (
                          'Send Image'
                        )}
                      </button>
                      <button
                        onClick={handleCancelImage}
                        className="px-3 py-1 bg-gray-500 text-white rounded-lg hover:bg-gray-600 text-sm"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                )}

                {/* Message Input */}
                <div className="p-3 sm:p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                  <div className="flex items-center space-x-2 sm:space-x-3">
                    {/* Image Options Button */}
                    <div className="relative">
                      <button
                        onClick={() => {
                          console.log('Image button clicked, current showImageOptions:', showImageOptions);
                          setShowImageOptions(!showImageOptions);
                        }}
                        className="p-1.5 sm:p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        <Image className="w-4 h-4 sm:w-5 sm:h-5" />
                      </button>

                      {/* Image Options Dropdown */}
                      {showImageOptions && (
                        <div className="absolute bottom-full left-0 mb-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 z-10">
                          <button
                            onClick={handleImageFromGallery}
                            className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                          >
                            <Image className="w-4 h-4" />
                            <span>Gallery</span>
                          </button>
                          <button
                            onClick={handleImageFromCamera}
                            className="flex items-center space-x-2 w-full px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                          >
                            <Camera className="w-4 h-4" />
                            <span>Camera</span>
                          </button>
                        </div>
                      )}
                    </div>
                    <div className="flex-1 relative">
                      <label htmlFor="messageInput" className="sr-only">Type a message</label>
                      <input
                        ref={messageInputRef}
                        type="text"
                        id="messageInput"
                        name="messageInput"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSendMessage();
                          }
                        }}
                        placeholder="Type a message..."
                        className="w-full px-3 sm:px-4 py-2.5 sm:py-3 border border-gray-300 dark:border-gray-600 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none text-base"
                      />
                    </div>
                    <button
                      onClick={handleSendMessage}
                      disabled={!newMessage.trim() || sendingMessage}
                      className="p-2.5 sm:p-3 bg-primary-500 text-white rounded-lg sm:rounded-xl hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {sendingMessage ? (
                        <Loader2 className="w-4 h-4 sm:w-5 sm:h-5 animate-spin" />
                      ) : (
                        <Send className="w-4 h-4 sm:w-5 sm:h-5" />
                      )}
                    </button>
                  </div>

                  {/* Safety Warning Banner */}
                  <div className="px-3 sm:px-4 py-2 bg-blue-50 dark:bg-blue-900/20 border-t border-blue-200 dark:border-blue-800">
                    <div className="flex items-center text-xs sm:text-sm text-blue-700 dark:text-blue-300">
                      <div className="flex-shrink-0 mr-2">
                        🔒
                      </div>
                      <span>
                        Payments and communication are protected when you stay in Hive Campus.
                      </span>
                    </div>
                  </div>

                  {/* Hidden file inputs */}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                  <input
                    ref={cameraInputRef}
                    type="file"
                    accept="image/*"
                    capture="environment"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>
              </div>
            ) : (
              <div className="hidden md:flex flex-1 items-center justify-center bg-gray-50 dark:bg-gray-900">
                <div className="text-center">
                  <MessageCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    Select a conversation
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Choose a chat from the sidebar to start messaging
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Messages;